# 视频字幕AI工具使用指南

## 🎯 项目概述

这是一个基于AI的视频字幕自动生成工具，支持命令行和GUI两种使用方式。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install PyQt6 faster-whisper

# 安装系统依赖 (macOS)
brew install ffmpeg mpv

# 安装系统依赖 (Ubuntu)
sudo apt install ffmpeg mpv
```

### 2. 启动GUI界面

```bash
python run_cli.py --gui
```

### 3. 使用GUI处理视频

1. **选择视频文件**: 点击"选择视频文件"按钮，选择要处理的视频
2. **配置设置**: 在"设置"选项卡中调整模型、语言等参数
3. **开始处理**: 点击"开始处理"按钮，等待处理完成
4. **播放视频**: 处理完成后，点击"播放视频"查看结果

## 📋 功能特性

### ✅ 已实现功能

- **视频音频提取**: 使用FFmpeg提取视频中的音频
- **AI语音识别**: 使用faster-whisper进行本地语音识别
- **字幕生成**: 自动生成SRT格式字幕文件
- **视频播放**: 使用MPV播放器播放带字幕的视频
- **GUI界面**: 用户友好的图形界面
- **异步处理**: 不阻塞界面的后台处理
- **实时日志**: 显示处理进度和状态信息

### 🔧 配置选项

- **模型选择**: tiny, base, small, medium, large
- **语言设置**: 自动检测或手动指定
- **设备选择**: CPU, CUDA, MPS (Apple Silicon)
- **字幕参数**: 最大长度、显示时间等

## 🎮 命令行使用

### 基本用法

```bash
# 处理单个视频文件
python run_cli.py video.mp4

# 指定输出目录
python run_cli.py video.mp4 -o output/

# 指定模型和语言
python run_cli.py video.mp4 --model base --language zh

# 不播放结果视频
python run_cli.py video.mp4 --no-play
```

### 交互式模式

```bash
# 启动后会提示输入视频路径
python run_cli.py
```

## 🔍 故障排除

### 常见问题

1. **MPV播放器问题**
   - 确保MPV已正确安装: `brew install mpv` (macOS)
   - 检查MPV版本: `mpv --version`
   - 如果只有声音没有视频窗口：
     * 在GUI中点击"播放视频"后选择"是"查看备用选项
     * 使用"显示手动播放命令"获取终端命令
     * 在终端中手动运行: `mpv "视频路径" --sub-file="字幕路径"`
   - macOS特殊问题：
     * 尝试: `mpv --vo=gpu --force-window 视频文件`
     * 或者: `mpv --vo=libmpv 视频文件`

2. **faster-whisper导入错误**
   - 安装依赖: `pip install faster-whisper`
   - 可能需要安装PyTorch: `pip install torch torchaudio`

3. **FFmpeg未找到**
   - 安装FFmpeg: `brew install ffmpeg` (macOS)
   - 确保FFmpeg在系统PATH中

4. **GUI启动失败**
   - 安装PyQt6: `pip install PyQt6`
   - 检查Python版本兼容性

### 性能优化

1. **模型选择**
   - `tiny`: 最快，准确度较低
   - `small`: 平衡速度和准确度（推荐）
   - `base/medium/large`: 更准确，但速度较慢

2. **设备选择**
   - `cpu`: 兼容性最好
   - `mps`: Apple Silicon Mac推荐
   - `cuda`: NVIDIA GPU推荐

## 📁 文件结构

```
video-subtitle-tool/
├── src/                    # 源代码
│   ├── gui/               # GUI界面
│   ├── services/          # 核心服务
│   ├── config/            # 配置管理
│   └── utils/             # 工具函数
├── models/                # AI模型文件
├── output/                # 输出字幕文件
├── resources/             # 测试资源
└── requirements.txt       # 依赖列表
```

## 🎯 下一步计划

- [ ] 字幕编辑功能
- [ ] 批量处理支持
- [ ] 更多字幕格式 (VTT, ASS)
- [ ] 字幕样式自定义
- [ ] 性能优化和GPU加速

## 📞 获取帮助

如果遇到问题或需要帮助，请：

1. 检查日志输出中的错误信息
2. 确认所有依赖都已正确安装
3. 尝试使用不同的模型或设置
4. 查看项目README.md获取更多信息
