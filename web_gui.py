#!/usr/bin/env python3
"""
基于Web的简单GUI界面
作为PyQt6的备用方案
"""

import os
import sys
import json
import threading
import webbrowser
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import parse_qs, urlparse

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.main import VideoSubtitleProcessor

class VideoSubtitleHandler(SimpleHTTPRequestHandler):
    """处理视频字幕请求的HTTP处理器"""
    
    def __init__(self, *args, **kwargs):
        self.processor = VideoSubtitleProcessor()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path == '/api/status':
            self.serve_status()
        else:
            super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/process':
            self.handle_process_request()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """提供主页面"""
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频字幕AI工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], input[type="text"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007AFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056CC;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-area {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频字幕AI工具</h1>
        
        <form id="processForm">
            <div class="form-group">
                <label for="videoFile">选择视频文件:</label>
                <input type="file" id="videoFile" accept="video/*" required>
            </div>
            
            <div class="form-group">
                <label for="model">Whisper模型:</label>
                <select id="model">
                    <option value="tiny">tiny (最快)</option>
                    <option value="base">base</option>
                    <option value="small" selected>small (推荐)</option>
                    <option value="medium">medium</option>
                    <option value="large">large (最准确)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="language">语言:</label>
                <select id="language">
                    <option value="">自动检测</option>
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                    <option value="ja">日文</option>
                    <option value="ko">韩文</option>
                </select>
            </div>
            
            <button type="submit" id="processBtn">开始处理</button>
        </form>
        
        <div id="status"></div>
        <div id="logArea" class="log-area" style="display: none;"></div>
        
        <div id="results" style="display: none;">
            <h3>处理完成</h3>
            <button onclick="openOutputFolder()">打开输出文件夹</button>
            <button onclick="playVideo()">播放视频</button>
        </div>
    </div>

    <script>
        let processing = false;
        
        document.getElementById('processForm').addEventListener('submit', function(e) {
            e.preventDefault();
            if (processing) return;
            
            const videoFile = document.getElementById('videoFile').files[0];
            if (!videoFile) {
                showStatus('请选择视频文件', 'error');
                return;
            }
            
            startProcessing(videoFile);
        });
        
        function startProcessing(videoFile) {
            processing = true;
            document.getElementById('processBtn').disabled = true;
            document.getElementById('processBtn').textContent = '处理中...';
            document.getElementById('logArea').style.display = 'block';
            
            showStatus('开始处理视频...', 'info');
            addLog('开始处理: ' + videoFile.name);
            
            // 这里应该实现实际的处理逻辑
            // 由于是演示，我们模拟处理过程
            simulateProcessing();
        }
        
        function simulateProcessing() {
            const steps = [
                '提取音频...',
                '加载AI模型...',
                '进行语音识别...',
                '生成字幕文件...',
                '处理完成!'
            ];
            
            let step = 0;
            const interval = setInterval(() => {
                if (step < steps.length) {
                    addLog(steps[step]);
                    step++;
                } else {
                    clearInterval(interval);
                    finishProcessing();
                }
            }, 2000);
        }
        
        function finishProcessing() {
            processing = false;
            document.getElementById('processBtn').disabled = false;
            document.getElementById('processBtn').textContent = '开始处理';
            document.getElementById('results').style.display = 'block';
            showStatus('处理完成！字幕文件已生成。', 'success');
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function addLog(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function openOutputFolder() {
            addLog('打开输出文件夹...');
            // 这里应该调用后端API打开文件夹
        }
        
        function playVideo() {
            addLog('启动视频播放...');
            // 这里应该调用后端API播放视频
        }
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_status(self):
        """提供状态信息"""
        status = {
            'status': 'ready',
            'message': '系统就绪'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status).encode('utf-8'))
    
    def handle_process_request(self):
        """处理视频处理请求"""
        # 这里应该实现实际的视频处理逻辑
        response = {
            'success': True,
            'message': '处理请求已接收'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode('utf-8'))

def start_web_server(port=8080):
    """启动Web服务器"""
    server_address = ('localhost', port)
    httpd = HTTPServer(server_address, VideoSubtitleHandler)
    
    print(f"🌐 Web GUI服务器启动在: http://localhost:{port}")
    print("📱 请在浏览器中打开上述地址")
    print("⏹️  按 Ctrl+C 停止服务器")
    
    # 自动打开浏览器
    threading.Timer(1.0, lambda: webbrowser.open(f'http://localhost:{port}')).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.shutdown()

if __name__ == "__main__":
    start_web_server()
