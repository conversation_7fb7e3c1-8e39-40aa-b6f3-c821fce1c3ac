# 视频字幕AI自动识别显示工具

一个基于AI的视频字幕自动生成和显示工具，使用faster-whisper进行本地语音识别，FFmpeg进行音视频处理，MPV进行视频播放。

## 项目特性

- 🎯 **准确的语音识别**: 使用faster-whisper模型进行本地ASR处理
- ⚡ **高效的音视频处理**: 基于FFmpeg的音频提取和处理
- 🎬 **集成视频播放**: 支持MPV播放器的字幕同步显示
- 🔧 **模块化设计**: 清晰的代码结构，易于维护和扩展
- 🌐 **多语言支持**: 支持多种语言的语音识别
- 📝 **标准字幕格式**: 生成标准SRT格式字幕文件

## 项目结构

```
video-subtitle-tool/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── models.py      # 数据模型
│   │   └── exceptions.py  # 异常定义
│   ├── config/            # 配置模块
│   │   └── settings.py    # 设置管理
│   ├── services/          # 服务层
│   │   ├── asr_service.py      # ASR服务
│   │   ├── ffmpeg_service.py   # FFmpeg服务
│   │   ├── subtitle_service.py # 字幕服务
│   │   └── player_service.py   # 播放器服务
│   ├── utils/             # 工具模块
│   │   ├── file_utils.py  # 文件工具
│   │   └── time_utils.py  # 时间工具
│   └── main.py           # 主程序入口
├── models/               # 模型文件目录
│   └── whisper_models/   # Whisper模型
├── output/              # 输出文件目录
├── resources/           # 资源文件目录
├── tests/              # 测试目录
├── requirements.txt    # 依赖列表
└── README.md          # 项目说明
```

## 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者手动安装核心依赖
pip install faster-whisper torch torchaudio numpy

# 安装GUI依赖（可选）
pip install PyQt6 python-mpv

# 确保系统已安装FFmpeg和MPV
# macOS: brew install ffmpeg mpv
# Ubuntu: sudo apt install ffmpeg mpv
# Windows: 下载并安装对应的可执行文件
```

## 使用方法

### 命令行模式

```bash
# 基本使用
python run_cli.py path/to/video.mp4

# 指定输出目录
python run_cli.py path/to/video.mp4 -o output_directory

# 指定模型和语言
python run_cli.py path/to/video.mp4 --model base --language zh

# 不播放结果视频
python run_cli.py path/to/video.mp4 --no-play

# 查看帮助
python run_cli.py --help
```

### 交互式模式

```bash
# 运行后会提示输入视频文件路径
python run_cli.py
```

### GUI模式（开发中）

```bash
# 启动GUI界面
python run_cli.py --gui
```

## 配置说明

主要配置在 `src/config/settings.py` 中：

- **模型配置**: 默认使用small模型，可选base、medium、large等
- **语言设置**: 支持自动检测或手动指定语言代码
- **设备选择**: 支持CPU、CUDA、MPS等设备
- **输出设置**: 字幕文件格式和保存路径

## 支持的格式

### 视频格式
- MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V

### 字幕格式
- SRT (当前支持)
- VTT, ASS (计划支持)

## 开发状态

### ✅ 已完成
- [x] 项目重构和模块化
- [x] 核心服务层实现
- [x] 命令行接口
- [x] 音频提取功能
- [x] ASR语音识别
- [x] SRT字幕生成
- [x] 基本播放功能

### 🚧 开发中
- [ ] GUI界面开发
- [ ] 异步处理和多线程
- [ ] 字幕编辑功能
- [ ] 更多字幕格式支持

### 📋 计划中
- [ ] 字幕样式自定义
- [ ] 批量处理功能
- [ ] 多语言界面
- [ ] 性能优化

## 测试

```bash
# 运行基础测试
python test_refactored.py

# 测试完整流程（需要有测试视频文件）
python run_cli.py resources/test.mp4
```

## 故障排除

### 常见问题

1. **FFmpeg未找到**
   - 确保FFmpeg已安装并在系统PATH中
   - macOS: `brew install ffmpeg`
   - Ubuntu: `sudo apt install ffmpeg`

2. **MPV未找到**
   - 确保MPV已安装并在系统PATH中
   - macOS: `brew install mpv`
   - Ubuntu: `sudo apt install mpv`

3. **faster-whisper导入错误**
   - 安装依赖: `pip install faster-whisper`
   - 可能需要安装PyTorch: `pip install torch torchaudio`

4. **模型文件未找到**
   - 确保Whisper模型文件在 `models/whisper_models/` 目录下
   - 检查模型文件夹名称是否与配置一致

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目采用MIT许可证。
