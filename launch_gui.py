#!/usr/bin/env python3
"""
GUI启动脚本
解决macOS上PyQt6的平台插件问题
"""

import os
import sys
from pathlib import Path

def setup_qt_environment():
    """设置Qt环境变量"""
    # 添加src目录到Python路径
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    sys.path.insert(0, str(src_path))
    
    # 设置Qt插件路径
    try:
        import PyQt6
        pyqt6_dir = Path(PyQt6.__file__).parent
        plugins_dir = pyqt6_dir / "Qt6" / "plugins"
        
        if plugins_dir.exists():
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(plugins_dir)
            print(f"Set QT_QPA_PLATFORM_PLUGIN_PATH to: {plugins_dir}")
        
        # 设置其他Qt环境变量
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        
        # 对于macOS，确保使用正确的平台
        if sys.platform == 'darwin':
            os.environ['QT_QPA_PLATFORM'] = 'cocoa'
            
    except ImportError:
        print("PyQt6 not found")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动视频字幕AI工具GUI...")
    
    # 设置Qt环境
    if not setup_qt_environment():
        print("❌ 环境设置失败")
        sys.exit(1)
    
    try:
        # 导入并启动GUI
        from src.gui.main_window import launch_gui
        print("✅ 环境设置成功，启动GUI...")
        launch_gui()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖：pip install PyQt6 faster-whisper")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
